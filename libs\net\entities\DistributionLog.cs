using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TNO.Entities;

/// <summary>
/// DistributionLog class, provides an entity to track content distribution events.
/// </summary>
[Table("distribution_log")]
public class DistributionLog : AuditColumns
{
    #region Properties
    /// <summary>
    /// get/set - Primary key, identity seed.
    /// </summary>
    [Key]
    [Column("id")]
    public long Id { get; set; }

    /// <summary>
    /// get/set - Unique distribution identifier.
    /// </summary>
    [Column("distribution_id")]
    public string DistributionId { get; set; } = "";

    /// <summary>
    /// get/set - Foreign key to content.
    /// </summary>
    [Column("content_id")]
    public long? ContentId { get; set; }

    /// <summary>
    /// get/set - The content that was distributed.
    /// </summary>
    public Content? Content { get; set; }

    /// <summary>
    /// get/set - Foreign key to report.
    /// </summary>
    [Column("report_id")]
    public int? ReportId { get; set; }

    /// <summary>
    /// get/set - The report that was distributed.
    /// </summary>
    public Report? Report { get; set; }

    /// <summary>
    /// get/set - Foreign key to user.
    /// </summary>
    [Column("user_id")]
    public int UserId { get; set; }

    /// <summary>
    /// get/set - The user who received the distribution.
    /// </summary>
    public User? User { get; set; }

    /// <summary>
    /// get/set - Recipient email address.
    /// </summary>
    [Column("recipient_email")]
    public string RecipientEmail { get; set; } = "";

    /// <summary>
    /// get/set - Sender email address.
    /// </summary>
    [Column("sender_email")]
    public string SenderEmail { get; set; } = "";

    /// <summary>
    /// get/set - Distribution timestamp.
    /// </summary>
    [Column("distribution_time")]
    public DateTime DistributionTime { get; set; }

    /// <summary>
    /// get/set - Email subject.
    /// </summary>
    [Column("subject")]
    public string Subject { get; set; } = "";

    /// <summary>
    /// get/set - Content hash for verification.
    /// </summary>
    [Column("content_hash")]
    public string ContentHash { get; set; } = "";

    /// <summary>
    /// get/set - Tracking identifier embedded in email.
    /// </summary>
    [Column("tracking_id")]
    public string TrackingId { get; set; } = "";

    /// <summary>
    /// get/set - Distribution format (FullText, LinkOnly, etc.).
    /// </summary>
    [Column("distribution_format")]
    public string DistributionFormat { get; set; } = "";

    /// <summary>
    /// get/set - Whether this is a test distribution.
    /// </summary>
    [Column("is_test")]
    public bool IsTest { get; set; } = false;

    /// <summary>
    /// get/set - Collection of tracking access events for this distribution.
    /// </summary>
    public virtual ICollection<TrackingAccess> TrackingAccesses { get; set; } = new List<TrackingAccess>();
    #endregion

    #region Constructors
    /// <summary>
    /// Creates a new instance of a DistributionLog object.
    /// </summary>
    protected DistributionLog() { }

    /// <summary>
    /// Creates a new instance of a DistributionLog object, initializes with specified parameters.
    /// </summary>
    /// <param name="distributionId"></param>
    /// <param name="userId"></param>
    /// <param name="recipientEmail"></param>
    /// <param name="distributionTime"></param>
    public DistributionLog(string distributionId, int userId, string recipientEmail, DateTime distributionTime)
    {
        this.DistributionId = distributionId;
        this.UserId = userId;
        this.RecipientEmail = recipientEmail;
        this.DistributionTime = distributionTime;
    }
    #endregion
}

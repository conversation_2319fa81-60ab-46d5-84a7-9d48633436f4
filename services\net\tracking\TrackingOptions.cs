using TNO.Services.Config;

namespace TNO.Services.Tracking;

/// <summary>
/// TrackingOptions class, configuration options for tracking service.
/// </summary>
public class TrackingOptions : ServiceOptions
{
    #region Properties
    /// <summary>
    /// get/set - The base URL for tracking pixel requests.
    /// </summary>
    public string TrackingBaseUrl { get; set; } = "https://mmi-tracking.gov.bc.ca";

    /// <summary>
    /// get/set - Whether to enable tracking pixel functionality.
    /// </summary>
    public bool EnableTrackingPixel { get; set; } = true;

    /// <summary>
    /// get/set - Whether to enable email distribution logging.
    /// </summary>
    public bool EnableDistributionLogging { get; set; } = true;

    /// <summary>
    /// get/set - Whether to enable violation detection.
    /// </summary>
    public bool EnableViolationDetection { get; set; } = true;

    /// <summary>
    /// get/set - Maximum number of allowed accesses per distribution.
    /// </summary>
    public int MaxAllowedAccesses { get; set; } = 5;

    /// <summary>
    /// get/set - Time window in hours for violation detection.
    /// </summary>
    public int ViolationDetectionWindowHours { get; set; } = 24;

    /// <summary>
    /// get/set - Email addresses to notify when violations are detected.
    /// </summary>
    public string ViolationNotificationEmails { get; set; } = "";

    /// <summary>
    /// get/set - Whether to store IP addresses for tracking.
    /// </summary>
    public bool StoreIpAddresses { get; set; } = true;

    /// <summary>
    /// get/set - Whether to store user agent information.
    /// </summary>
    public bool StoreUserAgent { get; set; } = true;
    #endregion
}

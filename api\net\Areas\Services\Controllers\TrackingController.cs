using System.Net;
using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.Annotations;
using TNO.API.Areas.Services.Models.Tracking;
using TNO.API.Models;
using TNO.Core.Exceptions;
using TNO.DAL.Services;
using TNO.Keycloak;

namespace TNO.API.Areas.Services.Controllers;

/// <summary>
/// TrackingController class, provides tracking endpoints for content distribution monitoring.
/// </summary>
[ClientRoleAuthorization(ClientRole.Administrator)]
[ApiController]
[Area("services")]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[area]/tracking")]
[Route("api/[area]/tracking")]
[Route("v{version:apiVersion}/[area]/tracking")]
[Route("[area]/tracking")]
[ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.Unauthorized)]
[ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.Forbidden)]
public class TrackingController : ControllerBase
{
    #region Variables
    private readonly ITrackingService _trackingService;
    private readonly JsonSerializerOptions _serializerOptions;
    #endregion

    #region Constructors
    /// <summary>
    /// Creates a new instance of a TrackingController object, initializes with arguments.
    /// </summary>
    /// <param name="trackingService"></param>
    /// <param name="serializerOptions"></param>
    public TrackingController(
        ITrackingService trackingService,
        IOptions<JsonSerializerOptions> serializerOptions)
    {
        _trackingService = trackingService;
        _serializerOptions = serializerOptions.Value;
    }
    #endregion

    #region Endpoints
    /// <summary>
    /// Handle tracking pixel requests.
    /// This endpoint serves a 1x1 transparent pixel and logs the access.
    /// </summary>
    /// <param name="trackingId">The unique tracking identifier</param>
    /// <returns>1x1 transparent PNG pixel</returns>
    [AllowAnonymous]
    [HttpGet("pixel/{trackingId}")]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerOperation(Tags = new[] { "Tracking" })]
    public async Task<IActionResult> TrackingPixelAsync(string trackingId)
    {
        try
        {
            // Extract client information
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            var userAgent = Request.Headers["User-Agent"].ToString();
            var referer = Request.Headers["Referer"].ToString();

            // Log the tracking request
            await _trackingService.LogTrackingAccessAsync(new TrackingAccessModel
            {
                TrackingId = trackingId,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                Referer = referer,
                AccessTime = DateTime.UtcNow
            });

            // Return 1x1 transparent PNG pixel
            var pixel = Convert.FromBase64String("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
            return File(pixel, "image/png");
        }
        catch (Exception ex)
        {
            // Log error but still return pixel to avoid breaking email display
            // Logger would be injected in real implementation
            Console.WriteLine($"Tracking error: {ex.Message}");
            
            var pixel = Convert.FromBase64String("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
            return File(pixel, "image/png");
        }
    }

    /// <summary>
    /// Log email distribution event.
    /// </summary>
    /// <param name="model">Distribution logging model</param>
    /// <returns></returns>
    [HttpPost("distribution")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(DistributionLogModel), (int)HttpStatusCode.Created)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Tracking" })]
    public async Task<IActionResult> LogDistributionAsync([FromBody] DistributionLogModel model)
    {
        var result = await _trackingService.LogDistributionAsync(model);
        return new JsonResult(result);
    }

    /// <summary>
    /// Get tracking statistics for a specific distribution.
    /// </summary>
    /// <param name="distributionId">Distribution ID</param>
    /// <returns></returns>
    [HttpGet("stats/{distributionId}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(TrackingStatsModel), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Tracking" })]
    public async Task<IActionResult> GetTrackingStatsAsync(string distributionId)
    {
        var stats = await _trackingService.GetTrackingStatsAsync(distributionId);
        return new JsonResult(stats);
    }

    /// <summary>
    /// Get violation report for a date range.
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns></returns>
    [HttpGet("violations")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(ViolationReportModel[]), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Tracking" })]
    public async Task<IActionResult> GetViolationReportAsync(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate)
    {
        var violations = await _trackingService.GetViolationReportAsync(startDate, endDate);
        return new JsonResult(violations);
    }
    #endregion
}

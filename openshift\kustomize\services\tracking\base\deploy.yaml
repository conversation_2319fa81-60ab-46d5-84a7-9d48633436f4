---
# How the app will be deployed to the pod.
kind: DeploymentConfig
apiVersion: apps.openshift.io/v1
metadata:
  name: tracking-service
  namespace: default
  annotations:
    description: Defines how to deploy tracking-service
    created-by: system
  labels:
    name: tracking-service
    part-of: tno
    version: 1.0.0
    component: tracking-service
    managed-by: kustomize
spec:
  replicas: 1
  selector:
    name: tracking-service
    part-of: tno
    component: tracking-service
  strategy:
    rollingParams:
      intervalSeconds: 1
      maxSurge: 25%
      maxUnavailable: 25%
      timeoutSeconds: 600
      updatePeriodSeconds: 1
    type: Rolling
  template:
    metadata:
      name: tracking-service
      labels:
        name: tracking-service
        part-of: tno
        component: tracking-service
    spec:
      containers:
        - name: tracking-service
          image: ""
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 50m
              memory: 128Mi
          env:
            - name: ASPNETCORE_ENVIRONMENT
              value: Production
            - name: ASPNETCORE_URLS
              value: http://+:8080
            - name: TZ
              value: America/Vancouver
          envFrom:
            - secretRef:
                name: tracking-service
            - configMapRef:
                name: tracking-service
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 60
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 60
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      securityContext: {}
      terminationGracePeriodSeconds: 30
  triggers:
    - type: ConfigChange
    - type: ImageChange
      imageChangeParams:
        automatic: true
        containerNames:
          - tracking-service
        from:
          kind: ImageStreamTag
          namespace: 9b301c-tools
          name: tracking-service:dev

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TNO.Entities;

/// <summary>
/// TrackingAccess class, provides an entity to track content access events.
/// </summary>
[Table("tracking_access")]
public class TrackingAccess : AuditColumns
{
    #region Properties
    /// <summary>
    /// get/set - Primary key, identity seed.
    /// </summary>
    [Key]
    [Column("id")]
    public long Id { get; set; }

    /// <summary>
    /// get/set - Unique tracking identifier.
    /// </summary>
    [Column("tracking_id")]
    public string TrackingId { get; set; } = "";

    /// <summary>
    /// get/set - Foreign key to distribution log.
    /// </summary>
    [Column("distribution_id")]
    public string DistributionId { get; set; } = "";

    /// <summary>
    /// get/set - IP address of the accessor.
    /// </summary>
    [Column("ip_address")]
    public string? IpAddress { get; set; }

    /// <summary>
    /// get/set - User agent string.
    /// </summary>
    [Column("user_agent")]
    public string? UserAgent { get; set; }

    /// <summary>
    /// get/set - Referer URL.
    /// </summary>
    [Column("referer")]
    public string? Referer { get; set; }

    /// <summary>
    /// get/set - Time of access.
    /// </summary>
    [Column("access_time")]
    public DateTime AccessTime { get; set; }

    /// <summary>
    /// get/set - Geographic location (if available).
    /// </summary>
    [Column("location")]
    public string? Location { get; set; }

    /// <summary>
    /// get/set - Device type (mobile, desktop, etc.).
    /// </summary>
    [Column("device_type")]
    public string? DeviceType { get; set; }

    /// <summary>
    /// get/set - Whether this access is considered suspicious.
    /// </summary>
    [Column("is_suspicious")]
    public bool IsSuspicious { get; set; } = false;
    #endregion

    #region Constructors
    /// <summary>
    /// Creates a new instance of a TrackingAccess object.
    /// </summary>
    protected TrackingAccess() { }

    /// <summary>
    /// Creates a new instance of a TrackingAccess object, initializes with specified parameters.
    /// </summary>
    /// <param name="trackingId"></param>
    /// <param name="distributionId"></param>
    /// <param name="accessTime"></param>
    public TrackingAccess(string trackingId, string distributionId, DateTime accessTime)
    {
        this.TrackingId = trackingId;
        this.DistributionId = distributionId;
        this.AccessTime = accessTime;
    }
    #endregion
}

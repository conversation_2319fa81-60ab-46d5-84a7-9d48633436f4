using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using TNO.Services.Runners;

namespace TNO.Services.Tracking;

/// <summary>
/// TrackingService class, provides a service for tracking content distribution and access.
/// This service handles tracking pixels, email distribution logging, and violation detection.
/// </summary>
public class TrackingService : BaseService
{
    #region Variables
    #endregion

    #region Properties
    #endregion

    #region Constructors
    /// <summary>
    /// Creates a new instance of a TrackingService object, initializes with arguments.
    /// </summary>
    /// <param name="args"></param>
    public TrackingService(string[] args) : base(args)
    {
    }
    #endregion

    #region Methods
    /// <summary>
    /// Configure dependency injection.
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    protected override IServiceCollection ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        services
            .Configure<TrackingOptions>(this.Configuration.GetSection("Service"))
            .AddSingleton<IServiceManager, TrackingManager>();

        return services;
    }
    #endregion
}

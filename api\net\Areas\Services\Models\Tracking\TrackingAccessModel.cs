namespace TNO.API.Areas.Services.Models.Tracking;

/// <summary>
/// TrackingAccessModel class, represents a tracking pixel access event.
/// </summary>
public class TrackingAccessModel
{
    #region Properties
    /// <summary>
    /// get/set - Unique tracking identifier.
    /// </summary>
    public string TrackingId { get; set; } = "";

    /// <summary>
    /// get/set - IP address of the accessor.
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// get/set - User agent string.
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// get/set - Referer URL.
    /// </summary>
    public string? Referer { get; set; }

    /// <summary>
    /// get/set - Time of access.
    /// </summary>
    public DateTime AccessTime { get; set; }

    /// <summary>
    /// get/set - Geographic location (if available).
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// get/set - Device type (mobile, desktop, etc.).
    /// </summary>
    public string? DeviceType { get; set; }
    #endregion
}

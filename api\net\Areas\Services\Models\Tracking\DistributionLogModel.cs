namespace TNO.API.Areas.Services.Models.Tracking;

/// <summary>
/// DistributionLogModel class, represents an email distribution event.
/// </summary>
public class DistributionLogModel
{
    #region Properties
    /// <summary>
    /// get/set - Unique distribution identifier.
    /// </summary>
    public string DistributionId { get; set; } = "";

    /// <summary>
    /// get/set - Content identifier.
    /// </summary>
    public long ContentId { get; set; }

    /// <summary>
    /// get/set - Report identifier.
    /// </summary>
    public int? ReportId { get; set; }

    /// <summary>
    /// get/set - Client identifier.
    /// </summary>
    public string ClientId { get; set; } = "";

    /// <summary>
    /// get/set - Recipient email address.
    /// </summary>
    public string RecipientEmail { get; set; } = "";

    /// <summary>
    /// get/set - Sender email address.
    /// </summary>
    public string SenderEmail { get; set; } = "";

    /// <summary>
    /// get/set - Distribution timestamp.
    /// </summary>
    public DateTime DistributionTime { get; set; }

    /// <summary>
    /// get/set - Email subject.
    /// </summary>
    public string Subject { get; set; } = "";

    /// <summary>
    /// get/set - Content hash for verification.
    /// </summary>
    public string ContentHash { get; set; } = "";

    /// <summary>
    /// get/set - Tracking identifier embedded in email.
    /// </summary>
    public string TrackingId { get; set; } = "";

    /// <summary>
    /// get/set - Distribution format (FullText, LinkOnly, etc.).
    /// </summary>
    public string DistributionFormat { get; set; } = "";

    /// <summary>
    /// get/set - Whether this is a test distribution.
    /// </summary>
    public bool IsTest { get; set; } = false;
    #endregion
}

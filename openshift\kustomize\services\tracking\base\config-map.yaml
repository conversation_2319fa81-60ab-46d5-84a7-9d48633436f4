---
# Configuration settings
kind: ConfigMap
apiVersion: v1
metadata:
  name: tracking-service
  namespace: default
  annotations:
    description: Tracking service configuration settings
    created-by: system
  labels:
    name: tracking-service
    part-of: tno
    version: 1.0.0
    component: tracking-service
    managed-by: kustomize
data:
  # Service Configuration
  Service__TrackingBaseUrl: "https://mmi-tracking.gov.bc.ca"
  Service__EnableTrackingPixel: "true"
  Service__EnableDistributionLogging: "true"
  Service__EnableViolationDetection: "true"
  Service__MaxAllowedAccesses: "5"
  Service__ViolationDetectionWindowHours: "24"
  Service__StoreIpAddresses: "true"
  Service__StoreUserAgent: "true"
  Service__DefaultDelayMS: "30000"
  Service__MaxFailLimit: "3"
  
  # Database Configuration
  ConnectionStrings__TNO: "Host=database;Port=5432;Database=tno;Include Error Detail=true;"
  
  # Logging Configuration
  Logging__LogLevel__Default: "Information"
  Logging__LogLevel__Microsoft: "Warning"
  Logging__LogLevel__Microsoft.Hosting.Lifetime: "Information"
  
  # Health Check Configuration
  HealthChecks__LivenessPath: "/health"
  HealthChecks__ReadinessPath: "/health/ready"
